#!/bin/bash

# Kubera Manthra Production Deployment Script
# Domain: thepostcloud.info
# Server: *************

set -e  # Exit on any error

echo "🚀 Kubera Manthra Production Deployment"
echo "======================================="
echo "Domain: thepostcloud.info"
echo "Server: *************"
echo "======================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [ "$EUID" -eq 0 ]; then
    print_warning "Running as root. Consider using a non-root user for security."
fi

# Update system packages
print_status "Updating system packages..."
sudo yum update -y

# Install Node.js if not installed
if ! command -v node &> /dev/null; then
    print_status "Installing Node.js 18.x..."
    curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
    sudo yum install -y nodejs
    print_success "Node.js installed: $(node --version)"
else
    print_success "Node.js already installed: $(node --version)"
fi

# Install Nginx if not installed
if ! command -v nginx &> /dev/null; then
    print_status "Installing Nginx..."
    sudo yum install -y nginx
    print_success "Nginx installed"
else
    print_success "Nginx already installed"
fi

# Install PM2 if not installed
if ! command -v pm2 &> /dev/null; then
    print_status "Installing PM2..."
    sudo npm install -g pm2
    print_success "PM2 installed: $(pm2 --version)"
else
    print_success "PM2 already installed: $(pm2 --version)"
fi

# Create application directory
APP_DIR="/var/www/kubera-manthra"
if [ ! -d "$APP_DIR" ]; then
    print_status "Creating application directory..."
    sudo mkdir -p "$APP_DIR"
    sudo chown $USER:$USER "$APP_DIR"
    print_success "Application directory created: $APP_DIR"
fi

# Install application dependencies
print_status "Installing application dependencies..."
npm install --production

# Create logs directory
mkdir -p logs

# Stop existing PM2 processes
print_status "Stopping existing PM2 processes..."
pm2 delete kubera-manthra 2>/dev/null || true

# Start application with PM2
print_status "Starting application with PM2..."
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Setup PM2 startup script
print_status "Setting up PM2 startup script..."
pm2 startup

# Configure Nginx
print_status "Configuring Nginx..."
sudo tee /etc/nginx/conf.d/kubera-manthra.conf > /dev/null << 'EOF'
server {
    listen 80;
    server_name thepostcloud.info www.thepostcloud.info *************;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # Main application proxy
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # API routes
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
        proxy_request_buffering off;
    }
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:3001;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/json;
}
EOF

# Test Nginx configuration
print_status "Testing Nginx configuration..."
if sudo nginx -t; then
    print_success "Nginx configuration is valid"
else
    print_error "Nginx configuration is invalid"
    exit 1
fi

# Start and enable Nginx
print_status "Starting Nginx..."
sudo systemctl start nginx
sudo systemctl enable nginx

# Wait for services to start
print_status "Waiting for services to initialize..."
sleep 5

# Test the deployment
print_status "Testing deployment..."

# Test direct API access
if curl -s -f http://localhost:3001/api/health > /dev/null; then
    print_success "✅ Direct API access working"
else
    print_error "❌ Direct API access failed"
fi

# Test through Nginx
if curl -s -f http://localhost/api/health > /dev/null; then
    print_success "✅ Nginx proxy working"
else
    print_warning "⚠️ Nginx proxy may need configuration"
fi

# Display deployment status
echo ""
echo "🎉 Deployment Complete!"
echo "======================="
echo "Application: Kubera Manthra"
echo "Domain: thepostcloud.info"
echo "Server IP: *************"
echo "Status: $(pm2 list | grep kubera-manthra | awk '{print $10}' || echo 'Unknown')"
echo ""
echo "🔗 Access URLs:"
echo "==============="
echo "Main Site: http://thepostcloud.info"
echo "API Health: http://thepostcloud.info/api/health"
echo "Direct IP: http://*************"
echo ""
echo "📊 Service Status:"
echo "=================="
pm2 status
echo ""
echo "📋 Management Commands:"
echo "======================="
echo "View logs: pm2 logs kubera-manthra"
echo "Restart: pm2 restart kubera-manthra"
echo "Stop: pm2 stop kubera-manthra"
echo "Monitor: pm2 monit"
echo ""
echo "🔒 Next Steps:"
echo "=============="
echo "1. Configure your domain DNS to point to *************"
echo "2. Set up SSL certificate: sudo certbot --nginx -d thepostcloud.info"
echo "3. Test your application in a web browser"
echo "4. Monitor logs and performance"

print_success "Kubera Manthra is now live at http://thepostcloud.info"
