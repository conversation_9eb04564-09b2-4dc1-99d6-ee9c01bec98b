#!/bin/bash

# Simple Server Starter for Kubera Manthra
# This script starts the server without complex dependencies

echo "🚀 Simple Server Starter for Kubera Manthra"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if we're in the right directory
if [ ! -f "server.js" ]; then
    print_error "server.js not found! Please run this script from your project directory."
    exit 1
fi

# Kill any existing processes
print_status "Stopping any existing server processes..."
pkill -f "server.js" 2>/dev/null || true
if [ -f "server.pid" ]; then
    kill $(cat server.pid) 2>/dev/null || true
    rm -f server.pid
fi

# Check for Node.js in common locations
NODE_CMD=""
if command -v node &> /dev/null; then
    NODE_CMD="node"
    print_success "Found node command: $(which node)"
elif command -v nodejs &> /dev/null; then
    NODE_CMD="nodejs"
    print_success "Found nodejs command: $(which nodejs)"
elif [ -f "/usr/local/bin/node" ]; then
    NODE_CMD="/usr/local/bin/node"
    print_success "Found node at: /usr/local/bin/node"
elif [ -f "/usr/bin/node" ]; then
    NODE_CMD="/usr/bin/node"
    print_success "Found node at: /usr/bin/node"
else
    print_error "Node.js not found! Please install Node.js first."
    echo "Run: chmod +x install-node.sh && ./install-node.sh"
    exit 1
fi

# Check Node.js version
NODE_VERSION=$($NODE_CMD --version 2>/dev/null)
if [ $? -eq 0 ]; then
    print_success "Node.js version: $NODE_VERSION"
else
    print_error "Node.js is not working properly"
    exit 1
fi

# Install dependencies if needed
if [ ! -d "node_modules" ]; then
    print_status "Installing dependencies..."
    if command -v npm &> /dev/null; then
        npm install
    else
        print_error "npm not found! Cannot install dependencies."
        exit 1
    fi
fi

# Set environment variables
export NODE_ENV=production
export PORT=3001

# Create logs directory
mkdir -p logs

print_status "Starting Kubera Manthra server..."
print_status "Node command: $NODE_CMD"
print_status "Environment: $NODE_ENV"
print_status "Port: $PORT"

# Start the server in background
$NODE_CMD server.js > logs/server.log 2>&1 &
SERVER_PID=$!

# Save PID
echo $SERVER_PID > server.pid
print_success "Server started with PID: $SERVER_PID"

# Wait a moment for server to initialize
print_status "Waiting for server to initialize..."
sleep 5

# Check if process is still running
if ps -p $SERVER_PID > /dev/null 2>&1; then
    print_success "✅ Server is running successfully!"
else
    print_error "❌ Server failed to start. Checking logs..."
    if [ -f "logs/server.log" ]; then
        echo "Last 20 lines of server log:"
        tail -20 logs/server.log
    fi
    exit 1
fi

# Test the server
print_status "Testing server endpoints..."

# Wait a bit more for server to be ready
sleep 3

# Test health endpoint
print_status "Testing health endpoint..."
if curl -s -f http://localhost:3001/api/health > /dev/null 2>&1; then
    print_success "✅ Health endpoint is working!"
    echo "Response:"
    curl -s http://localhost:3001/api/health | head -5
else
    print_warning "⚠️ Health endpoint test failed"
    print_status "Checking if server is listening on port 3001..."
    netstat -tlnp | grep 3001 || print_warning "Port 3001 is not listening"
fi

# Display server information
echo ""
echo "📊 Server Status:"
echo "================="
echo "PID: $SERVER_PID"
echo "Status: $(ps -p $SERVER_PID > /dev/null 2>&1 && echo 'Running' || echo 'Not running')"
echo "Port: 3001"
echo "Environment: $NODE_ENV"
echo "Log file: logs/server.log"

echo ""
echo "🔗 Test URLs:"
echo "============="
echo "Health check: http://localhost:3001/api/health"
echo "Zodiac signs: http://localhost:3001/api/zodiac-signs"
echo "Main site: http://localhost:3001/"

echo ""
echo "📋 Useful Commands:"
echo "==================="
echo "Check status: ps -p \$(cat server.pid)"
echo "View logs: tail -f logs/server.log"
echo "Stop server: kill \$(cat server.pid)"
echo "Test API: curl http://localhost:3001/api/health"

echo ""
echo "🔧 If server stops working:"
echo "==========================="
echo "1. Check logs: tail -20 logs/server.log"
echo "2. Restart: ./simple-start.sh"
echo "3. Check port: netstat -tlnp | grep 3001"

print_success "Server startup completed!"

# Show recent log entries
if [ -f "logs/server.log" ]; then
    echo ""
    echo "📝 Recent log entries:"
    echo "====================="
    tail -10 logs/server.log
fi
