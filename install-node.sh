#!/bin/bash

# Node.js Installation Script for AWS EC2 Amazon Linux
echo "🚀 Installing Node.js on AWS EC2"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check current system
print_status "Checking current system..."
cat /etc/os-release | head -2

# Check if Node.js is already installed
print_status "Checking for existing Node.js installation..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_success "Node.js is already installed: $NODE_VERSION"
    print_status "Node.js location: $(which node)"
else
    print_warning "Node.js is not installed or not in PATH"
fi

# Check if npm is available
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    print_success "npm is available: $NPM_VERSION"
else
    print_warning "npm is not available"
fi

# Update system packages
print_status "Updating system packages..."
sudo yum update -y

# Install Node.js using NodeSource repository (recommended method)
print_status "Installing Node.js 18.x from NodeSource..."

# Download and run the NodeSource setup script
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -

# Install Node.js
sudo yum install -y nodejs

# Verify installation
print_status "Verifying Node.js installation..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_success "✅ Node.js installed successfully: $NODE_VERSION"
    print_status "Node.js location: $(which node)"
else
    print_error "❌ Node.js installation failed"
    
    # Try alternative installation method
    print_status "Trying alternative installation method..."
    
    # Install using Amazon Linux Extras (if available)
    if command -v amazon-linux-extras &> /dev/null; then
        print_status "Using Amazon Linux Extras..."
        sudo amazon-linux-extras install -y nodejs npm
    else
        # Manual installation
        print_status "Manual installation from official binaries..."
        cd /tmp
        wget https://nodejs.org/dist/v18.19.0/node-v18.19.0-linux-x64.tar.xz
        tar -xf node-v18.19.0-linux-x64.tar.xz
        sudo cp -r node-v18.19.0-linux-x64/* /usr/local/
        
        # Add to PATH
        echo 'export PATH=/usr/local/bin:$PATH' >> ~/.bashrc
        source ~/.bashrc
    fi
fi

# Verify npm installation
print_status "Verifying npm installation..."
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    print_success "✅ npm installed successfully: $NPM_VERSION"
    print_status "npm location: $(which npm)"
else
    print_error "❌ npm installation failed"
fi

# Update npm to latest version
print_status "Updating npm to latest version..."
sudo npm install -g npm@latest

# Install PM2 globally
print_status "Installing PM2 process manager..."
sudo npm install -g pm2

# Verify PM2 installation
if command -v pm2 &> /dev/null; then
    PM2_VERSION=$(pm2 --version)
    print_success "✅ PM2 installed successfully: $PM2_VERSION"
else
    print_warning "⚠️ PM2 installation may have failed"
fi

# Display final status
echo ""
echo "📊 Installation Summary:"
echo "======================="
echo "Node.js: $(node --version 2>/dev/null || echo 'Not found')"
echo "npm: $(npm --version 2>/dev/null || echo 'Not found')"
echo "PM2: $(pm2 --version 2>/dev/null || echo 'Not found')"

echo ""
echo "📍 Installation Paths:"
echo "====================="
echo "Node.js: $(which node 2>/dev/null || echo 'Not found')"
echo "npm: $(which npm 2>/dev/null || echo 'Not found')"
echo "PM2: $(which pm2 2>/dev/null || echo 'Not found')"

echo ""
echo "🔧 Next Steps:"
echo "=============="
echo "1. Close and reopen your terminal (or run: source ~/.bashrc)"
echo "2. Verify installation: node --version"
echo "3. Navigate to your project directory"
echo "4. Run: npm install"
echo "5. Start your application: node server.js"

print_success "Node.js installation completed!"
