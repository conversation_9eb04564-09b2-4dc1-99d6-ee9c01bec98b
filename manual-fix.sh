#!/bin/bash

# Manual Fix Script for Kubera Manthra
# This script manually fixes the PM2 and server issues

echo "🔧 Manual Fix for Kubera Manthra"
echo "================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Kill any existing Node processes
print_status "Killing any existing Node.js processes..."
pkill -f "node" 2>/dev/null || true
sleep 2

# Step 2: Check if we have the necessary files
if [ ! -f "server.js" ]; then
    print_error "server.js not found! Make sure you're in the right directory."
    exit 1
fi

if [ ! -f "package.json" ]; then
    print_error "package.json not found! Make sure you're in the right directory."
    exit 1
fi

# Step 3: Install dependencies
print_status "Installing/updating dependencies..."
npm install

# Step 4: Start the server directly with Node.js (bypass PM2 for now)
print_status "Starting server directly with Node.js..."

# Create a simple start script
cat > start-server.sh << 'EOF'
#!/bin/bash
export NODE_ENV=production
export PORT=3001
nohup node server.js > server.log 2>&1 &
echo $! > server.pid
echo "Server started with PID: $(cat server.pid)"
EOF

chmod +x start-server.sh
./start-server.sh

# Step 5: Wait for server to start
print_status "Waiting for server to start..."
sleep 5

# Step 6: Check if server is running
if ps -p $(cat server.pid 2>/dev/null) > /dev/null 2>&1; then
    print_success "Server is running with PID: $(cat server.pid)"
else
    print_error "Server failed to start. Checking logs..."
    if [ -f "server.log" ]; then
        echo "Last 10 lines of server.log:"
        tail -10 server.log
    fi
    exit 1
fi

# Step 7: Test the API endpoints
print_status "Testing API endpoints..."
sleep 2

# Test health endpoint
if curl -s -f http://localhost:3001/api/health > /dev/null 2>&1; then
    print_success "✅ Health endpoint is working!"
    curl -s http://localhost:3001/api/health | head -3
else
    print_error "❌ Health endpoint is not working"
fi

# Test zodiac signs endpoint
if curl -s -f http://localhost:3001/api/zodiac-signs > /dev/null 2>&1; then
    print_success "✅ Zodiac signs endpoint is working!"
else
    print_error "❌ Zodiac signs endpoint is not working"
fi

# Step 8: Check what's listening on port 3001
print_status "Checking what's listening on port 3001..."
netstat -tlnp | grep 3001 || print_warning "Nothing listening on port 3001"

# Step 9: Install and configure Nginx
print_status "Installing Nginx..."
sudo yum install -y nginx

if [ $? -eq 0 ]; then
    print_success "Nginx installed successfully"
    
    # Create Nginx configuration
    print_status "Configuring Nginx..."
    sudo tee /etc/nginx/conf.d/kubera-manthra.conf > /dev/null << 'EOF'
server {
    listen 80;
    server_name _;
    
    # Main application
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # API routes
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
    }
}
EOF
    
    # Test Nginx configuration
    if sudo nginx -t; then
        print_success "Nginx configuration is valid"
        sudo systemctl start nginx
        sudo systemctl enable nginx
        print_success "Nginx started and enabled"
    else
        print_error "Nginx configuration is invalid"
    fi
else
    print_error "Failed to install Nginx"
fi

# Step 10: Final tests
print_status "Running final tests..."

# Test direct API access
echo ""
echo "🧪 Testing Direct API Access:"
echo "=============================="
curl -s http://localhost:3001/api/health && echo "" || echo "❌ Direct API test failed"

# Test through Nginx (if available)
if systemctl is-active --quiet nginx; then
    echo ""
    echo "🧪 Testing Through Nginx:"
    echo "========================="
    curl -s http://localhost/api/health && echo "" || echo "❌ Nginx proxy test failed"
fi

# Step 11: Display status and useful information
echo ""
echo "📊 Current Status:"
echo "=================="
echo "Server PID: $(cat server.pid 2>/dev/null || echo 'Not found')"
echo "Server Log: $(ls -la server.log 2>/dev/null || echo 'Not found')"
echo "Nginx Status: $(systemctl is-active nginx 2>/dev/null || echo 'Not running')"

echo ""
echo "🔗 Test URLs:"
echo "============="
echo "Direct API: http://localhost:3001/api/health"
echo "Through Nginx: http://your-domain.com/api/health"
echo "Main site: http://your-domain.com/"

echo ""
echo "📋 Useful Commands:"
echo "==================="
echo "Check server: ps -p \$(cat server.pid)"
echo "View logs: tail -f server.log"
echo "Stop server: kill \$(cat server.pid)"
echo "Restart server: ./start-server.sh"
echo "Check Nginx: sudo systemctl status nginx"
echo "Nginx logs: sudo tail -f /var/log/nginx/error.log"

echo ""
echo "🔧 If you need to restart everything:"
echo "====================================="
echo "1. kill \$(cat server.pid)  # Stop the server"
echo "2. ./start-server.sh        # Start the server"
echo "3. sudo systemctl restart nginx  # Restart Nginx"

print_success "Manual fix completed!"
