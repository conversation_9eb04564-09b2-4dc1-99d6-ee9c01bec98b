# Kubera Manthra - Production Hosting Guide

## Server Details
- **AWS EC2 IP**: *************
- **Domain**: thepostcloud.info
- **Application Port**: 3001
- **Environment**: Production

## Prerequisites

### 1. AWS EC2 Security Group Configuration
Configure your EC2 Security Group to allow the following inbound traffic:
- **HTTP (80)**: 0.0.0.0/0
- **HTTPS (443)**: 0.0.0.0/0
- **Custom TCP (3001)**: 0.0.0.0/0
- **SSH (22)**: Your IP address

### 2. Domain DNS Configuration
Point your domain to your EC2 instance:
- **A Record**: thepostcloud.info → *************
- **CNAME Record**: www.thepostcloud.info → thepostcloud.info

## Installation Steps

### Step 1: Connect to Your EC2 Instance
```bash
ssh -i your-key.pem ec2-user@*************
```

### Step 2: Update System and Install Dependencies
```bash
# Update system packages
sudo yum update -y

# Install Node.js 18.x
curl -fsSL https://rpm.nodesource.com/setup_18.x | sudo bash -
sudo yum install -y nodejs

# Install Nginx
sudo yum install -y nginx

# Install PM2 globally
sudo npm install -g pm2

# Verify installations
node --version
npm --version
pm2 --version
nginx -v
```

### Step 3: Upload Your Application
```bash
# Create application directory
sudo mkdir -p /var/www/kubera-manthra
sudo chown ec2-user:ec2-user /var/www/kubera-manthra

# Upload your files (use scp, git, or other method)
# Example with scp:
# scp -i your-key.pem -r . ec2-user@*************:/var/www/kubera-manthra/
```

### Step 4: Install Application Dependencies
```bash
cd /var/www/kubera-manthra
npm install --production
```

### Step 5: Configure Environment
Ensure your `.env` file has the correct production settings:
```env
# Google Gemini API Configuration
GEMINI_API_KEY=AIzaSyBdazvi0etIk23H6Fn7nNpU1BS55H9NNVc

# Server Configuration
PORT=3001
NODE_ENV=production

# Production Configuration
DOMAIN=thepostcloud.info
SERVER_IP=*************
```

### Step 6: Start Application with PM2
```bash
# Create logs directory
mkdir -p logs

# Start the application
pm2 start ecosystem.config.js --env production

# Save PM2 configuration
pm2 save

# Setup PM2 to start on system boot
pm2 startup
# Follow the instructions provided by the command above
```

### Step 7: Configure Nginx Reverse Proxy
Create Nginx configuration:
```bash
sudo tee /etc/nginx/conf.d/kubera-manthra.conf > /dev/null << 'EOF'
server {
    listen 80;
    server_name thepostcloud.info www.thepostcloud.info *************;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header X-Content-Type-Options "nosniff" always;
    
    # Main application proxy
    location / {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
    
    # API routes with specific configuration
    location /api/ {
        proxy_pass http://127.0.0.1:3001;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_buffering off;
        proxy_request_buffering off;
    }
    
    # Static files caching
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        proxy_pass http://127.0.0.1:3001;
        proxy_set_header Host $host;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/javascript
        application/json;
}
EOF
```

### Step 8: Start and Configure Nginx
```bash
# Test Nginx configuration
sudo nginx -t

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx

# Check status
sudo systemctl status nginx
```

## Testing Your Deployment

### 1. Test Direct API Access
```bash
curl http://localhost:3001/api/health
curl http://localhost:3001/api/zodiac-signs
```

### 2. Test Through Nginx
```bash
curl http://*************/api/health
curl http://thepostcloud.info/api/health
```

### 3. Test in Browser
- http://thepostcloud.info
- http://thepostcloud.info/api/health
- http://*************

## SSL/HTTPS Setup (Recommended)

### Install SSL Certificate with Let's Encrypt
```bash
# Install certbot
sudo yum install -y certbot python3-certbot-nginx

# Get SSL certificate
sudo certbot --nginx -d thepostcloud.info -d www.thepostcloud.info

# Test auto-renewal
sudo certbot renew --dry-run

# Set up auto-renewal cron job
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

## Monitoring and Maintenance

### PM2 Commands
```bash
pm2 status                    # Check application status
pm2 logs kubera-manthra      # View application logs
pm2 restart kubera-manthra   # Restart application
pm2 stop kubera-manthra      # Stop application
pm2 delete kubera-manthra    # Remove from PM2
pm2 monit                    # Real-time monitoring
```

### System Monitoring
```bash
# Check system resources
htop
df -h
free -h

# Check nginx status
sudo systemctl status nginx

# View nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Log Management
```bash
# Application logs
tail -f /var/www/kubera-manthra/logs/combined.log

# Setup log rotation
pm2 install pm2-logrotate
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

## Troubleshooting

### Common Issues and Solutions

1. **502 Bad Gateway**
   - Check if Node.js application is running: `pm2 status`
   - Restart application: `pm2 restart kubera-manthra`

2. **API 404 Errors**
   - Verify nginx configuration: `sudo nginx -t`
   - Check application logs: `pm2 logs kubera-manthra`

3. **SSL Certificate Issues**
   - Renew certificate: `sudo certbot renew`
   - Check certificate status: `sudo certbot certificates`

4. **High Memory Usage**
   - Monitor with: `pm2 monit`
   - Restart if needed: `pm2 restart kubera-manthra`

## Backup and Updates

### Regular Backups
```bash
# Backup application files
tar -czf kubera-manthra-backup-$(date +%Y%m%d).tar.gz /var/www/kubera-manthra

# Backup nginx configuration
sudo cp /etc/nginx/conf.d/kubera-manthra.conf ~/nginx-backup.conf
```

### Application Updates
```bash
cd /var/www/kubera-manthra
git pull origin main  # or upload new files
npm install --production
pm2 restart kubera-manthra
```

## Performance Optimization

### Enable Nginx Caching
Add to your nginx configuration:
```nginx
proxy_cache_path /var/cache/nginx levels=1:2 keys_zone=app_cache:10m max_size=1g inactive=60m;
proxy_cache app_cache;
proxy_cache_valid 200 1h;
```

### PM2 Cluster Mode (Optional)
For high traffic, use cluster mode:
```bash
pm2 start ecosystem.config.js --env production -i max
```

## Support and Maintenance

Your Kubera Manthra application is now running in production at:
- **Primary URL**: http://thepostcloud.info
- **Direct IP**: http://*************
- **API Health**: http://thepostcloud.info/api/health

For ongoing support, monitor the application regularly and keep all components updated.
