#!/bin/bash

# Kubera Manthra Diagnostic Script
# This script helps diagnose deployment issues on AWS EC2

echo "🔍 Kubera Manthra Deployment Diagnostics"
echo "========================================"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[CHECK]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check Node.js installation
print_status "Checking Node.js installation..."
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    print_success "Node.js is installed: $NODE_VERSION"
else
    print_error "Node.js is not installed"
fi

# Check npm installation
print_status "Checking npm installation..."
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    print_success "npm is installed: $NPM_VERSION"
else
    print_error "npm is not installed"
fi

# Check PM2 installation
print_status "Checking PM2 installation..."
if command -v pm2 &> /dev/null; then
    PM2_VERSION=$(pm2 --version)
    print_success "PM2 is installed: $PM2_VERSION"
else
    print_warning "PM2 is not installed (recommended for production)"
fi

# Check if application dependencies are installed
print_status "Checking application dependencies..."
if [ -d "node_modules" ]; then
    print_success "Node modules are installed"
else
    print_error "Node modules are not installed. Run: npm install"
fi

# Check environment file
print_status "Checking environment configuration..."
if [ -f ".env" ]; then
    print_success ".env file exists"
    
    # Check for required environment variables
    if grep -q "GEMINI_API_KEY" .env; then
        print_success "GEMINI_API_KEY is configured"
    else
        print_error "GEMINI_API_KEY is missing in .env file"
    fi
    
    if grep -q "NODE_ENV=production" .env; then
        print_success "NODE_ENV is set to production"
    else
        print_warning "NODE_ENV is not set to production"
    fi
else
    print_error ".env file is missing"
fi

# Check if server is running
print_status "Checking if server is running..."
if pgrep -f "node.*server.js" > /dev/null; then
    print_success "Node.js server process is running"
else
    print_error "Node.js server is not running"
fi

# Check if port 3001 is listening
print_status "Checking if port 3001 is listening..."
if netstat -tlnp 2>/dev/null | grep -q ":3001 "; then
    print_success "Port 3001 is listening"
else
    print_error "Port 3001 is not listening"
fi

# Test API endpoints
print_status "Testing API endpoints..."

# Test health endpoint
if curl -s -f http://localhost:3001/api/health > /dev/null 2>&1; then
    print_success "Health endpoint is accessible"
else
    print_error "Health endpoint is not accessible"
fi

# Test zodiac signs endpoint
if curl -s -f http://localhost:3001/api/zodiac-signs > /dev/null 2>&1; then
    print_success "Zodiac signs endpoint is accessible"
else
    print_error "Zodiac signs endpoint is not accessible"
fi

# Check nginx installation and status
print_status "Checking web server configuration..."
if command -v nginx &> /dev/null; then
    print_success "Nginx is installed"
    
    if systemctl is-active --quiet nginx; then
        print_success "Nginx is running"
    else
        print_warning "Nginx is installed but not running"
    fi
else
    print_warning "Nginx is not installed (recommended for production)"
fi

# Check firewall/security groups
print_status "Checking network connectivity..."
if curl -s -f http://localhost/api/health > /dev/null 2>&1; then
    print_success "Web server proxy is working"
else
    print_warning "Web server proxy may not be configured properly"
fi

# Check system resources
print_status "Checking system resources..."
MEMORY_USAGE=$(free | grep Mem | awk '{printf("%.1f%%", $3/$2 * 100.0)}')
DISK_USAGE=$(df -h / | awk 'NR==2{printf "%s", $5}')
print_success "Memory usage: $MEMORY_USAGE"
print_success "Disk usage: $DISK_USAGE"

# Check logs if PM2 is running
if command -v pm2 &> /dev/null; then
    print_status "Checking PM2 processes..."
    pm2 list
    
    echo ""
    print_status "Recent application logs:"
    pm2 logs kubera-manthra --lines 10 --nostream 2>/dev/null || echo "No PM2 logs available"
fi

echo ""
echo "🔧 Recommended Actions:"
echo "======================"

# Provide recommendations based on findings
if ! pgrep -f "node.*server.js" > /dev/null; then
    echo "1. Start the Node.js server:"
    echo "   pm2 start ecosystem.config.js --env production"
    echo "   OR"
    echo "   npm run prod"
fi

if ! netstat -tlnp 2>/dev/null | grep -q ":3001 "; then
    echo "2. Ensure the server is listening on port 3001"
    echo "   Check for port conflicts or firewall issues"
fi

if ! curl -s -f http://localhost:3001/api/health > /dev/null 2>&1; then
    echo "3. Fix API endpoint accessibility:"
    echo "   Check server logs for errors"
    echo "   Verify environment configuration"
fi

if ! command -v nginx &> /dev/null; then
    echo "4. Install and configure Nginx for production:"
    echo "   sudo yum install -y nginx"
    echo "   Configure reverse proxy to port 3001"
fi

echo ""
echo "📋 Quick Commands:"
echo "=================="
echo "Start server:     pm2 start ecosystem.config.js --env production"
echo "Check status:     pm2 status"
echo "View logs:        pm2 logs"
echo "Restart server:   pm2 restart kubera-manthra"
echo "Test API:         curl http://localhost:3001/api/health"
echo "Check processes:  ps aux | grep node"
echo "Check ports:      netstat -tlnp | grep 3001"
