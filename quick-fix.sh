#!/bin/bash

# Quick Fix Script for Kubera Manthra AWS Deployment Issues
# This script addresses the immediate 404 API errors and deployment issues

echo "🚀 Quick Fix for Kubera Manthra Deployment Issues"
echo "================================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Step 1: Stop any existing processes
print_status "Stopping existing Node.js processes..."
pkill -f "node.*server.js" 2>/dev/null || true
if command -v pm2 &> /dev/null; then
    pm2 delete all 2>/dev/null || true
fi
print_success "Existing processes stopped"

# Step 2: Install dependencies if missing
if [ ! -d "node_modules" ]; then
    print_status "Installing Node.js dependencies..."
    npm install
    print_success "Dependencies installed"
fi

# Step 3: Create PM2 ecosystem file if missing
if [ ! -f "ecosystem.config.js" ]; then
    print_status "Creating PM2 ecosystem configuration..."
    cat > ecosystem.config.js << 'EOF'
module.exports = {
  apps: [{
    name: 'kubera-manthra',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
};
EOF
    print_success "PM2 ecosystem file created"
fi

# Step 4: Create logs directory
mkdir -p logs

# Step 5: Install PM2 if not installed
if ! command -v pm2 &> /dev/null; then
    print_status "Installing PM2..."
    sudo npm install -g pm2
    print_success "PM2 installed"
fi

# Step 6: Start the application
print_status "Starting Kubera Manthra application..."
pm2 start ecosystem.config.js --env production

# Step 7: Save PM2 configuration
pm2 save

# Step 8: Set up PM2 startup script
print_status "Setting up PM2 startup script..."
pm2 startup

# Step 9: Test the API endpoints
print_status "Testing API endpoints..."
sleep 3  # Give the server time to start

# Test health endpoint
if curl -s -f http://localhost:3001/api/health > /dev/null 2>&1; then
    print_success "✅ Health endpoint is working: http://localhost:3001/api/health"
else
    print_error "❌ Health endpoint is not accessible"
fi

# Test zodiac signs endpoint
if curl -s -f http://localhost:3001/api/zodiac-signs > /dev/null 2>&1; then
    print_success "✅ Zodiac signs endpoint is working: http://localhost:3001/api/zodiac-signs"
else
    print_error "❌ Zodiac signs endpoint is not accessible"
fi

# Step 10: Configure basic Nginx if available
if command -v nginx &> /dev/null; then
    print_status "Configuring Nginx reverse proxy..."
    
    # Create a simple nginx configuration
    sudo tee /etc/nginx/conf.d/kubera-manthra.conf > /dev/null << 'EOF'
server {
    listen 80;
    server_name _;
    
    location / {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }
}
EOF
    
    # Test nginx configuration
    if sudo nginx -t 2>/dev/null; then
        sudo systemctl restart nginx
        print_success "Nginx configured and restarted"
    else
        print_warning "Nginx configuration test failed"
    fi
else
    print_warning "Nginx not installed. Install it for production use."
fi

# Step 11: Display status and next steps
echo ""
echo "📊 Current Status:"
echo "=================="
pm2 status

echo ""
echo "🔗 Test URLs:"
echo "============="
echo "Direct API access:"
echo "  Health: http://localhost:3001/api/health"
echo "  Zodiac: http://localhost:3001/api/zodiac-signs"
echo ""
echo "Through web server (if Nginx is configured):"
echo "  Health: http://your-domain.com/api/health"
echo "  Main site: http://your-domain.com/"

echo ""
echo "📋 Useful Commands:"
echo "==================="
echo "Check status:     pm2 status"
echo "View logs:        pm2 logs kubera-manthra"
echo "Restart app:      pm2 restart kubera-manthra"
echo "Stop app:         pm2 stop kubera-manthra"
echo "Test API:         curl http://localhost:3001/api/health"

echo ""
echo "🔧 If you still have issues:"
echo "============================"
echo "1. Check the logs: pm2 logs kubera-manthra"
echo "2. Verify your .env file has the correct API key"
echo "3. Ensure your AWS Security Group allows traffic on ports 80 and 3001"
echo "4. Run the diagnostic script: ./diagnose.sh"

print_success "Quick fix completed! Your application should now be running."
