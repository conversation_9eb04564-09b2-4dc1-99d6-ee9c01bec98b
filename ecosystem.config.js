module.exports = {
  apps: [{
    name: 'kubera-manthra',
    script: 'server.js',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true,
    // Restart policy
    min_uptime: '10s',
    max_restarts: 10,
    // Advanced PM2 features
    merge_logs: true,
    log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
    // Environment variables for production
    env_production: {
      NODE_ENV: 'production',
      PORT: 3001,
      // Add any other production-specific environment variables here
    }
  }],

  deploy: {
    production: {
      user: 'ec2-user',
      host: 'your-ec2-host',
      ref: 'origin/main',
      repo: 'your-git-repo',
      path: '/home/<USER>/kubera-manthra',
      'pre-deploy-local': '',
      'post-deploy': 'npm install && pm2 reload ecosystem.config.js --env production',
      'pre-setup': ''
    }
  }
};
