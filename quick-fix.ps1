# Quick Fix PowerShell Script for Kubera Manthra
# This script helps prepare files for AWS deployment

Write-Host "🚀 Kubera Manthra - Preparing for AWS Deployment" -ForegroundColor Blue
Write-Host "=================================================" -ForegroundColor Blue

# Check if we're in the right directory
if (-not (Test-Path "server.js")) {
    Write-Host "❌ Error: server.js not found. Please run this script from the project root directory." -ForegroundColor Red
    exit 1
}

Write-Host "✅ Found server.js - we're in the right directory" -ForegroundColor Green

# Check if .env file exists
if (-not (Test-Path ".env")) {
    Write-Host "⚠️  Warning: .env file not found. Creating from .env.example..." -ForegroundColor Yellow
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
        Write-Host "✅ Created .env file from .env.example" -ForegroundColor Green
    } else {
        Write-Host "❌ Error: .env.example not found either!" -ForegroundColor Red
    }
} else {
    Write-Host "✅ Found .env file" -ForegroundColor Green
}

# Check Node.js installation
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js is installed: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Error: Node.js is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Node.js from https://nodejs.org/" -ForegroundColor Yellow
    exit 1
}

# Check npm installation
try {
    $npmVersion = npm --version
    Write-Host "✅ npm is installed: $npmVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Error: npm is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Install dependencies if node_modules doesn't exist
if (-not (Test-Path "node_modules")) {
    Write-Host "📦 Installing Node.js dependencies..." -ForegroundColor Blue
    npm install
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Dependencies installed successfully" -ForegroundColor Green
    } else {
        Write-Host "❌ Error: Failed to install dependencies" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "✅ Node modules already installed" -ForegroundColor Green
}

# Test the server locally
Write-Host "🧪 Testing server locally..." -ForegroundColor Blue
Write-Host "Starting server in background..." -ForegroundColor Yellow

# Start the server in background
$serverProcess = Start-Process -FilePath "node" -ArgumentList "server.js" -PassThru -WindowStyle Hidden

# Wait a moment for server to start
Start-Sleep -Seconds 3

# Test the health endpoint
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3001/api/health" -UseBasicParsing -TimeoutSec 10
    if ($response.StatusCode -eq 200) {
        Write-Host "✅ Server is working! Health endpoint responded with status 200" -ForegroundColor Green
    } else {
        Write-Host "⚠️  Server responded but with status: $($response.StatusCode)" -ForegroundColor Yellow
    }
} catch {
    Write-Host "❌ Error: Could not connect to server on localhost:3001" -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
}

# Stop the test server
if ($serverProcess -and !$serverProcess.HasExited) {
    $serverProcess.Kill()
    Write-Host "🛑 Stopped test server" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "📋 Files prepared for AWS deployment:" -ForegroundColor Blue
Write-Host "=====================================" -ForegroundColor Blue
Write-Host "✅ deploy-aws.sh - Main deployment script"
Write-Host "✅ quick-fix.sh - Quick fix for common issues"
Write-Host "✅ diagnose.sh - Diagnostic script"
Write-Host "✅ nginx-config.conf - Nginx configuration"
Write-Host "✅ AWS_DEPLOYMENT_GUIDE.md - Detailed deployment guide"
Write-Host "✅ ecosystem.config.js - PM2 configuration (if created)"

Write-Host ""
Write-Host "🚀 Next Steps for AWS Deployment:" -ForegroundColor Blue
Write-Host "==================================" -ForegroundColor Blue
Write-Host "1. Upload all files to your AWS EC2 instance"
Write-Host "2. SSH into your EC2 instance"
Write-Host "3. Navigate to your project directory"
Write-Host "4. Run: chmod +x *.sh"
Write-Host "5. Run: ./quick-fix.sh"
Write-Host "6. Test: curl http://localhost:3001/api/health"
Write-Host ""
Write-Host "📖 For detailed instructions, see AWS_DEPLOYMENT_GUIDE.md" -ForegroundColor Green

Write-Host ""
Write-Host "🔧 Common AWS EC2 Commands:" -ForegroundColor Blue
Write-Host "===========================" -ForegroundColor Blue
Write-Host "# Upload files to EC2:"
Write-Host "scp -i your-key.pem -r . ec2-user@your-ec2-ip:/home/<USER>/kubera-manthra/"
Write-Host ""
Write-Host "# SSH to EC2:"
Write-Host "ssh -i your-key.pem ec2-user@your-ec2-ip"
Write-Host ""
Write-Host "# On EC2, run:"
Write-Host "cd kubera-manthra"
Write-Host "chmod +x *.sh"
Write-Host "./quick-fix.sh"

Write-Host ""
Write-Host "✅ Preparation complete! Ready for AWS deployment." -ForegroundColor Green
